@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add the extracted CSS below */
@layer components {
:root {
    /* Premium Color Variables */
    --primary: #6366f1; /* Enhanced Indigo-500 */
    --primary-light: #a5b4fc; /* Indigo-300 */
    --primary-dark: #4338ca; /* Indigo-700 */
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --secondary: #06b6d4; /* Cyan-500 */
    --secondary-light: #67e8f9; /* Cyan-300 */
    --accent: #f59e0b; /* Amber-500 */
    --accent-light: #fbbf24; /* Amber-400 */
    --success: #10b981; /* Emerald-500 */
    --success-light: #34d399; /* Emerald-400 */
    --danger: #ef4444; /* Red-500 */
    --danger-light: #f87171; /* Red-400 */
    --info: #3b82f6; /* Blue-500 */
    --warning: #f59e0b; /* Amber-500 */

    /* Premium Neutral Palette */
    --neutral-50: #fafafa; /* Neutral-50 */
    --neutral-100: #f5f5f5; /* Neutral-100 */
    --neutral-200: #e5e5e5; /* Neutral-200 */
    --neutral-300: #d4d4d4; /* Neutral-300 */
    --neutral-400: #a3a3a3; /* Neutral-400 */
    --neutral-500: #737373; /* Neutral-500 */
    --neutral-600: #525252; /* Neutral-600 */
    --neutral-700: #404040; /* Neutral-700 */
    --neutral-800: #262626; /* Neutral-800 */
    --neutral-900: #171717; /* Neutral-900 */
    --neutral-950: #0a0a0a; /* Neutral-950 */

    /* Premium Background Gradients */
    --bg-gradient-light: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    --bg-gradient-dark: linear-gradient(135deg, #171717 0%, #262626 100%);
    --card-gradient-light: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
    --card-gradient-dark: linear-gradient(135deg, rgba(38,38,38,0.9) 0%, rgba(64,64,64,0.7) 100%);

    /* Glass Morphism Variables */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --glass-backdrop: blur(10px);

    /* Premium Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.04);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
    --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.16);
    --shadow-premium: 0 20px 60px rgba(0, 0, 0, 0.2);

    /* Enhanced Transition Variables */
    --transition-duration: 0.3s;
    --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-theme: background-color var(--transition-duration) var(--transition-timing),
                        color var(--transition-duration) var(--transition-timing),
                        border-color var(--transition-duration) var(--transition-timing),
                        box-shadow var(--transition-duration) var(--transition-timing),
                        backdrop-filter var(--transition-duration) var(--transition-timing);
    --transition-interactive: all 0.2s var(--transition-timing);
    --transition-transform: transform 0.3s var(--transition-bounce);
    --transition-premium: all 0.4s var(--transition-timing);
}
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-gradient-light);
    color: var(--neutral-800);
    transition: var(--transition-theme);
    min-height: 100vh;
    position: relative;
}

/* Premium background pattern overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

header {
    transition: var(--transition-theme);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}
/* Modern Split Layout Styles */
@media (min-width: 1024px) {
    .split-layout {
        display: flex;
        gap: 1.5rem;
    }

    .split-layout-main {
        width: 66.666667%;
    }

    .split-layout-sidebar {
        width: 33.333333%;
    }
}

/* Enhanced Grid Layout Support - Robust 3-Column Layout */
.top-row-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    width: 100%;
    max-width: 100%;
    /* Ensure proper containment and prevent overlap */
    position: relative;
    isolation: isolate;
    /* Prevent grid items from overflowing */
    overflow: visible;
    /* Ensure proper z-index stacking */
    z-index: 1;
}

/* Premium Card Grid with improved spacing and alignment */
.premium-card-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem; /* Consistent gap for mobile */
    width: 100%;
    max-width: 100%;
}

@media (min-width: 768px) {
    .top-row-grid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
        grid-template-areas: "col1 col2 col3";
        /* Ensure equal column widths and proper spacing */
        grid-auto-rows: auto; /* Let content define row height */
        align-items: start;
        /* Increase gap for better separation */
        gap: 2.5rem; /* Consistent gap for desktop */
    }

    .premium-card-grid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 2.5rem; /* Consistent gap for desktop */
    }

    .top-row-grid > *:nth-child(1) {
        grid-area: col1;
        /* Ensure proper containment within grid cell */
        min-width: 0;
        max-width: 100%;
        /* Prevent overlap with z-index */
        z-index: 2;
        position: relative;
    }

    .top-row-grid > *:nth-child(2) {
        grid-area: col2;
        min-width: 0;
        max-width: 100%;
        /* Prevent overlap with z-index */
        z-index: 2;
        position: relative;
    }

    .top-row-grid > *:nth-child(3) {
        grid-area: col3;
        min-width: 0;
        max-width: 100%;
        /* Prevent overlap with z-index */
        z-index: 2;
        position: relative;
    }
}

/* Fallback: Enhanced Grid Layout Support for existing classes */
.grid.grid-cols-1.md\:grid-cols-3 {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    width: 100%;
    max-width: 100%;
}

@media (min-width: 768px) {
    .grid.grid-cols-1.md\:grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
        grid-template-areas: "col1 col2 col3";
    }

    .grid.grid-cols-1.md\:grid-cols-3 > *:nth-child(1) {
        grid-area: col1;
    }

    .grid.grid-cols-1.md\:grid-cols-3 > *:nth-child(2) {
        grid-area: col2;
    }

    .grid.grid-cols-1.md\:grid-cols-3 > *:nth-child(3) {
        grid-area: col3;
    }
}

/* Premium Card Styles */
.card-container {
    background: var(--card-gradient-light);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border-radius: 1rem;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    height: 100%;
    transition: var(--transition-premium);
    position: relative;
    max-width: 100%;
    /* Change overflow to visible to prevent input field clipping */
    overflow: visible;
    /* Ensure proper grid behavior */
    min-width: 0;
    width: 100%;
    /* Prevent flex behavior when in grid */
    flex: none;
    /* Ensure cards maintain their grid position and don't overlap */
    contain: layout style;
    /* Establish proper stacking context - higher z-index */
    z-index: 10;
    /* Ensure proper isolation */
    isolation: isolate;
}

/* Premium card glow effect */
.card-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    border-radius: inherit;
    transition: var(--transition-premium);
    z-index: -1;
}

.card-container:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px) scale(1.01);
    border-color: rgba(99, 102, 241, 0.3);
}

.card-container:hover::before {
    opacity: 0.05;
}

.card-container.dragging {
    box-shadow: var(--shadow-premium);
    opacity: 0.95;
    transform: scale(1.03) rotate(1deg);
    cursor: grabbing !important;
    z-index: 1000;
    border-color: var(--primary);
}

.card-container.dragging::before {
    opacity: 0.1;
}

.drag-handle {
    opacity: 0;
    transition: opacity 0.2s ease;
    cursor: grab;
}

.card-container:hover .drag-handle {
    opacity: 1;
}

/* Default positioning for cards on desktop - only applied when dragging */
@media (min-width: 768px) {
    .dragging[data-draggable-id="innerText-jobSpecs"],
    .dragging[data-draggable-id="cover-specs"],
    .dragging[data-draggable-id="endpapers-specs"],
    .dragging[data-draggable-id="innerText-prodParams"],
    .dragging[data-draggable-id="cover-prodParams"],
    .dragging[data-draggable-id="endpapers-prodParams"],
    .dragging[data-draggable-id="innerText-unitConverter"],
    .dragging[data-draggable-id="cover-unitConverter"],
    .dragging[data-draggable-id="endpapers-unitConverter"] {
        position: absolute;
        z-index: 1000;
        /* Ensure dragged cards maintain proper dimensions */
        box-shadow: var(--shadow-lg);
        transform: scale(1.02);
    }
}

/* Ensure proper spacing between cards in grid layout */
.top-row-grid .card-container:not(.dragging) {
    /* Prevent any margin collapse issues */
    margin: 0;
    /* Ensure proper grid cell containment */
    justify-self: stretch;
    align-self: start;
}

/* Fix any potential stacking context issues */
.card-container .card-header,
.card-container .card-content,
.card-container .space-y-4 {
    position: relative;
    z-index: auto;
}

body.dark-mode .card-container {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);
}

/* Unit Converter Card Styles */
.unit-converter-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Premium Card Styles */
.card {
    background: var(--card-gradient-light);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--glass-border);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: var(--transition-premium);
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    border-radius: inherit;
    transition: var(--transition-premium);
    z-index: -1;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: rgba(99, 102, 241, 0.2);
}

.card:hover::before {
    opacity: 0.03;
}

.card-header {
    border-bottom: 1px solid rgba(99, 102, 241, 0.1);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
    transition: var(--transition-theme);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--primary-gradient);
    border-radius: 1px;
    transition: var(--transition-premium);
}

.card-header h2 {
    font-weight: 600;
    color: var(--neutral-800);
    font-size: 1.25rem;
    margin: 0;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
input[type=number] { -moz-appearance: textfield; appearance: textfield; }
/* Premium Form Styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: 0.75rem;
    font-size: 0.875rem;
    transition: var(--transition-premium);
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--neutral-800);
    max-width: 100%;
    box-shadow: var(--shadow-sm);
}

.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: 0.75rem;
    font-size: 0.875rem;
    box-sizing: border-box;
    font-family: inherit;
    line-height: inherit;
    color: inherit;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: var(--transition-premium);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding-right: 3rem;
    max-width: 100%;
    box-shadow: var(--shadow-sm);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236366f1'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1.25em 1.25em;
}

.form-input::placeholder {
    color: var(--neutral-400);
    font-size: 0.8rem;
    transition: var(--transition-theme);
}

.form-input:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-md);
    outline: none;
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
}

.input-error {
    border-color: var(--danger) !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--neutral-700);
    margin-bottom: 0.5rem;
    transition: var(--transition-theme);
    letter-spacing: 0.025em;
}
.conversion-icon {
    flex-shrink: 0;
    color: var(--secondary);
    transition: var(--transition-theme), transform var(--transition-duration) var(--transition-timing);
    padding: 0.375rem;
    border-radius: 0.5rem;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.2);
    width: 2.25rem;
    height: 2.25rem;
    display: flex !important;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    /* Ensure visibility */
    opacity: 1;
    visibility: visible;
}

.conversion-icon:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: var(--primary);
    background-color: rgba(255, 255, 255, 0.95);
}

/* Ensure SVG icons within conversion icons are visible */
.conversion-icon svg {
    display: block !important;
    width: 1rem;
    height: 1rem;
    stroke: currentColor;
    fill: none;
    opacity: 1;
    visibility: visible;
}

body.dark-mode .conversion-icon {
    color: var(--neutral-400);
    background-color: rgba(38, 38, 38, 0.8);
    border-color: rgba(99, 102, 241, 0.3);
}
body.dark-mode .conversion-icon:hover {
    background-color: rgba(64, 64, 64, 0.9);
    border-color: var(--primary-light);
}

/* Ensure dark mode SVG icons are visible */
body.dark-mode .conversion-icon svg {
    color: var(--neutral-300);
    stroke: currentColor;
}


/* Custom scrollbar styles */
::-webkit-scrollbar { width: 10px; height: 10px; }
::-webkit-scrollbar-track { background: var(--neutral-200); }
::-webkit-scrollbar-thumb { background-color: var(--neutral-400); border-radius: 5px; }
body.dark-mode { scrollbar-color: var(--neutral-600) var(--neutral-800); scrollbar-width: thin; }
body.dark-mode ::-webkit-scrollbar-track { background: var(--neutral-800); }
body.dark-mode ::-webkit-scrollbar-thumb { background-color: var(--neutral-600); }

.table-container {
    border-radius: 0.375rem; border: 1px solid var(--neutral-200);
    transition: var(--transition-theme); flex-grow: 1; margin-bottom: 0.2rem;
    overflow-x: auto; -webkit-overflow-scrolling: touch;
}
table { width: 100%; border-collapse: collapse; font-size: 0.875rem; table-layout: fixed; }
th {
    background-color: var(--neutral-50); font-weight: 600; text-align: left;
    padding: 0.75rem 1rem; border-bottom: 1px solid var(--neutral-200);
    white-space: nowrap;
    transition: var(--transition-theme);
}
td {
    padding: 0.5rem 1rem; border-bottom: 1px solid var(--neutral-200);
    vertical-align: middle; transition: var(--transition-theme);
}
td .form-input, td .form-select { max-width: 100%; }
tr:last-child td { border-bottom: none; }

th.th-paper-name, td.td-paper-name { width: 180px; }
th.th-source, td.td-source { width: 120px; }
th.th-sheet-h, td.td-sheet-h { width: 100px; }
th.th-sheet-w, td.td-sheet-w { width: 100px; }
th.th-grain-dir, td.td-grain-dir { width: 110px; }
th.th-caliper, td.td-caliper { width: 90px; }
th.th-cost-ream, td.td-cost-ream { width: 110px; }
th.th-gsm, td.td-gsm { width: 90px; }
th.th-cost-tonne, td.td-cost-tonne { width: 100px; }
th.th-actions, td.td-actions { width: 80px; text-align: center; }

/* Premium Result Card Styles */
.result-card {
    background: var(--card-gradient-light);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border-radius: 1.25rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--glass-border);
    position: relative;
    padding-top: 2rem;
    transition: var(--transition-premium);
    min-height: 420px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    border-radius: inherit;
    transition: var(--transition-premium);
    z-index: -1;
}

.result-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px) scale(1.02);
    border-color: rgba(99, 102, 241, 0.3);
}

.result-card:hover::before {
    opacity: 0.05;
}

.result-label {
    font-size: 0.75rem;
    color: var(--neutral-500);
    margin-bottom: 0.25rem;
    transition: var(--transition-theme);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.result-value {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--neutral-800);
    word-wrap: break-word;
    white-space: normal;
    transition: var(--transition-theme);
    line-height: 1.4;
}

.result-value-small {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--neutral-600);
    transition: var(--transition-theme);
}

.result-note {
    font-size: 0.75rem;
    font-style: italic;
    color: var(--neutral-500);
    display: inline;
    margin-left: 0.25rem;
    transition: var(--transition-theme);
}

.grain-aligned {
    color: var(--success);
    font-weight: 600;
}

.grain-misaligned {
    color: var(--danger);
    font-weight: 600;
}

.best-option {
    border: 2px solid var(--accent);
    box-shadow: var(--shadow-lg), 0 0 0 4px rgba(245, 158, 11, 0.1);
}

.best-option-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, var(--accent) 0%, var(--accent-light) 100%);
    color: white;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.5rem 1rem;
    border-radius: 0 1.25rem 0 1rem;
    z-index: 5;
    box-shadow: var(--shadow-md);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Premium Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-premium);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: var(--shadow-sm);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--neutral-700);
    border: 1px solid var(--glass-border);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.delete-row {
    color: #ef4444;
    background: none;
    border: none;
    padding: 0.25rem;
    cursor: pointer;
    transition: var(--transition-interactive), background-color var(--transition-duration) var(--transition-timing);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 0.25rem;
}
.delete-row:hover {
    color: #dc2626;
    background-color: rgba(239, 68, 68, 0.1);
}
.delete-row svg {
    width: 1.25rem;
    height: 1.25rem;
    pointer-events: none; /* Ensure clicks pass through to the button */
}
.delete-row svg path {
    pointer-events: none; /* Ensure clicks pass through to the button */
}
.btn svg { width: 1rem; height: 1rem; }

.lang-switch, .theme-switch, .alignment-switch, .double-lip-switch { /* Common switch base */
    position: relative; display: inline-block; cursor: pointer;
    transition: var(--transition-theme), transform var(--transition-duration) var(--transition-timing);
    overflow: hidden;
    -webkit-tap-highlight-color: transparent; user-select: none; outline: none;
}

/* Premium Switch Enhancement - Fixed to not override switch styles */
.premium-switch {
    /* Remove conflicting styles that override switch-specific styling */
    /* backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md), inset 0 1px 0 rgba(255, 255, 255, 0.1); */
}
.lang-switch:before, .theme-switch:before, .alignment-switch:before, .double-lip-switch:before {
    content: ''; position: absolute; top: -10px; left: -10px; right: -10px; bottom: -10px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0; transition: opacity var(--transition-duration) var(--transition-timing); pointer-events: none;
}
.lang-switch:hover:before, .theme-switch:hover:before, .alignment-switch:hover:before, .double-lip-switch:hover:before { opacity: 1; }

.lang-switch {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
    padding: 0.25rem; border-radius: 9999px;
    width: 88px; height: 40px;
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.2);
    position: relative; /* Ensure position is set */
    cursor: pointer;
}
.lang-switch.active {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.2);
}
.lang-switch:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.5), inset 0 2px 4px rgba(255, 255, 255, 0.3);
}
.lang-switch.active:hover {
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.5), inset 0 2px 4px rgba(255, 255, 255, 0.3);
}
.lang-switch-label {
    font-size: 0.75rem; font-weight: 600; z-index: 1; position: absolute;
    top: 50%; transform: translateY(-50%); transition: var(--transition-theme);
    color: rgba(255, 255, 255, 0.7); letter-spacing: 0.5px;
}
.lang-switch-label.en { left: 15px; } .lang-switch-label.zh { right: 15px; }
.lang-switch-toggle {
    width: 30px; height: 30px; background: #ffffff; border-radius: 9999px;
    position: absolute; top: 3px; left: 3px;
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1); /* Keep the bouncy effect for toggle */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); z-index: 2;
}
.lang-switch-toggle:after {
    content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
    width: 16px; height: 16px; background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 50%; opacity: 0.15; transition: opacity var(--transition-duration) var(--transition-timing);
}
.lang-switch.active .lang-switch-toggle { transform: translateX(44px); }
.lang-switch.active .lang-switch-label.en { color: rgba(255, 255, 255, 0.7); }
.lang-switch.active .lang-switch-label.zh { color: #ffffff; font-weight: 800; }
.lang-switch:not(.active) .lang-switch-label.en { color: #ffffff; font-weight: 800; }
.lang-switch:not(.active) .lang-switch-label.zh { color: rgba(255, 255, 255, 0.7); }
.lang-switch:not(.active):active .lang-switch-toggle { transform: scale(0.95); }
.lang-switch.active:active .lang-switch-toggle { transform: translateX(44px) scale(0.95); }
.lang-switch:active .lang-switch-toggle:after { opacity: 0.3; }

.theme-switch {
    background: linear-gradient(135deg, #f59e0b, #fbbf24); padding: 0.25rem; border-radius: 9999px;
    width: 88px; height: 40px;
    box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.2);
    position: relative; /* Ensure position is set */
    cursor: pointer;
}
.theme-switch:hover {
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.5), inset 0 2px 4px rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}
.theme-switch.dark {
    background: linear-gradient(135deg, #1f2937, #374151);
    box-shadow: 0 6px 16px rgba(31, 41, 55, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.1);
}
.theme-switch.dark:hover {
    box-shadow: 0 8px 20px rgba(31, 41, 55, 0.5), inset 0 2px 4px rgba(255, 255, 255, 0.2);
}
.theme-icon {
    position: absolute; top: 50%; transform: translateY(-50%); z-index: 1;
    transition: var(--transition-theme); color: rgba(255, 255, 255, 0.9); opacity: 1 !important;
}
.theme-icon.sun { width: 20px; height: 20px; left: 12px; }
.theme-icon.moon { width: 16px; height: 16px; right: 12px; }
.theme-switch-toggle {
    width: 30px; height: 30px; background: #ffffff; border-radius: 9999px;
    position: absolute; top: 3px; left: 3px;
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1); /* Keep the bouncy effect for toggle */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); z-index: 2;
    display: flex; align-items: center; justify-content: center;
}
.theme-switch-toggle:after {
    content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
    width: 16px; height: 16px; background: linear-gradient(135deg, #f59e0b, #fbbf24);
    border-radius: 50%; opacity: 0.15; transition: opacity var(--transition-duration) var(--transition-timing), background var(--transition-duration) var(--transition-timing);
}
.theme-switch.dark .theme-switch-toggle { transform: translateX(44px); }
.theme-switch.dark .theme-switch-toggle:after { background: linear-gradient(135deg, #1f2937, #374151); }
.theme-switch:not(.dark):active .theme-switch-toggle { transform: scale(0.95); }
.theme-switch.dark:active .theme-switch-toggle { transform: translateX(44px) scale(0.95); }

/* Note: Switch styles moved to consolidated section below to avoid conflicts */

/* Premium Dark Mode Styles */
body.dark-mode {
    background: var(--bg-gradient-dark);
    color: var(--neutral-100);
}

body.dark-mode::before {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%);
}

body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 {
    color: var(--neutral-100);
}

body.dark-mode p, body.dark-mode .dark-mode-text {
    color: var(--neutral-300);
}

body.dark-mode header {
    background: var(--card-gradient-dark);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

body.dark-mode .card {
    background: var(--card-gradient-dark);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-mode .card::before {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
}

body.dark-mode .card-container {
    background: var(--card-gradient-dark);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-mode .card-container::before {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
}

body.dark-mode .card-header {
    border-bottom: 1px solid rgba(99, 102, 241, 0.2);
}

body.dark-mode .card-header h2 {
    color: var(--neutral-100);
}
body.dark-mode .parameters-tabs {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);
}
body.dark-mode .parameters-tabs button[aria-selected="true"]::after {
    background-color: var(--primary-light);
}
body.dark-mode .parameters-tabs button:hover::after {
    background-color: var(--primary-light);
}
body.dark-mode .form-input, body.dark-mode .form-select {
    background: rgba(38, 38, 38, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-color: rgba(99, 102, 241, 0.3);
    color: var(--neutral-100);
}

body.dark-mode .form-input::placeholder {
    color: var(--neutral-500);
}

body.dark-mode .form-input:focus, body.dark-mode .form-select:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.2), var(--shadow-md);
    background: rgba(38, 38, 38, 0.95);
}

body.dark-mode .form-select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23a5b4fc'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

body.dark-mode .form-label {
    color: var(--neutral-300);
}
body.dark-mode .table-container { border-color: var(--neutral-700); }
body.dark-mode th { background-color: var(--neutral-800); border-bottom-color: var(--neutral-700); color: var(--neutral-200); }
body.dark-mode td { border-bottom-color: var(--neutral-700); color: var(--neutral-300); }
body.dark-mode .result-card {
    background: var(--card-gradient-dark);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
}

body.dark-mode .result-card::before {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
}

body.dark-mode .result-label {
    color: var(--neutral-400);
}

body.dark-mode .result-value {
    color: var(--neutral-100);
}

body.dark-mode .result-value-small {
    color: var(--neutral-400);
}

body.dark-mode .result-note {
    color: var(--neutral-500);
}
body.dark-mode .btn-secondary { background-color: var(--neutral-700); color: var(--neutral-200); border-color: var(--neutral-600); }
body.dark-mode .btn-secondary:hover { background-color: var(--neutral-600); }
body.dark-mode .modal-content { background-color: var(--neutral-800); box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); }
body.dark-mode .modal-message { color: var(--neutral-300); }
body.dark-mode .delete-row { color: var(--danger); }
body.dark-mode .delete-row:hover {
    color: #ff7f7f;
    background-color: rgba(239, 68, 68, 0.15);
}
body.dark-mode .converter-divider { border-color: var(--neutral-700); }

.modal-overlay {
    position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.6);
    display: flex; align-items: center; justify-content: center; z-index: 1000;
    opacity: 0; visibility: hidden; transition: opacity var(--transition-duration) var(--transition-timing),
                                               visibility var(--transition-duration) var(--transition-timing);
}
.modal-overlay.visible { opacity: 1; visibility: visible; }
.modal-content {
    background-color: white; padding: 1.5rem 2rem; border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); max-width: 400px; text-align: center;
    transition: var(--transition-theme);
}
.modal-message { margin-bottom: 1rem; color: var(--neutral-700); transition: var(--transition-theme); }

@keyframes textFadeOut { from { opacity: 1; transform: translateY(0); } to { opacity: 0; transform: translateY(-4px); } }
@keyframes textFadeIn { from { opacity: 0; transform: translateY(4px); } to { opacity: 1; transform: translateY(0); } }
.text-transition-out { animation: textFadeOut 0.12s ease-out forwards; }
.text-transition-in { animation: textFadeIn 0.16s ease-in forwards; }
[data-translate-key] { display: inline-block; vertical-align: bottom; }
h1[data-translate-key], h2[data-translate-key], h3[data-translate-key], p[data-translate-key], label[data-translate-key], span[data-translate-key], button > span[data-translate-key] { display: inline-block; }
header h1[data-translate-key="appTitle"], header p[data-translate-key="appSubtitle"] { display: block; } /* Adjusted for new keys */
.card-header h2[data-translate-key] { display: block; }
th[data-translate-key] { display: table-cell; }
td[data-translate-key] > div { display: block; }
label.form-label[data-translate-key] { display: block; }
button[data-translate-key] > span { vertical-align: middle; }

.card-header.collapsible {
    cursor: pointer; display: flex; justify-content: space-between; align-items: center;
    padding-right: 0.75rem; transition: var(--transition-theme);
}
.card-header.collapsible:hover { background-color: var(--neutral-100); }
body.dark-mode .card-header.collapsible:hover { background-color: var(--neutral-700); }
.card-content { overflow: hidden; transition: max-height var(--transition-duration) ease-out; }
.card-content.collapsed { max-height: 0 !important; padding-top: 0; padding-bottom: 0; margin-bottom: 0; } /* Ensure full collapse */
.toggle-icon { transition: var(--transition-transform); color: var(--neutral-500); }
.toggle-icon.collapsed { transform: rotate(180deg); }

/* Ensure input containers maintain proper width */
.relative.flex-1 {
    max-width: 100%;
    min-width: 0;
    flex-shrink: 1;
}

/* Enhanced input field sizing for proper layout */
.form-input, .form-select {
    /* Set minimum width to ensure readability */
    min-width: 120px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    /* Ensure inputs can shrink but maintain minimum usable width */
    flex-shrink: 1;
    flex-grow: 1;
}

/* Ensure flex containers provide adequate space */
.flex.items-center.gap-3 {
    width: 100%;
    /* Provide adequate minimum width for flex containers */
    min-width: 280px;
    overflow: visible;
    /* Ensure flex items get proper space distribution */
    align-items: center;
}

.flex.items-center.gap-3 > div {
    /* Allow flex items to grow and shrink properly */
    flex: 1;
    min-width: 120px;
    max-width: 100%;
}

/* Fix specific input container issues */
.relative.flex-1 {
    /* Ensure relative containers provide adequate space */
    flex: 1;
    min-width: 120px;
    max-width: 100%;
    overflow: visible;
}

/* Ensure grid containers within cards behave properly */
.card-container .grid {
    min-width: 0;
    max-width: 100%;
    /* Prevent grid overflow */
    overflow: visible;
}

.card-container .grid > div {
    min-width: 0;
    max-width: 100%;
}

/* Specific fixes for form layouts within cards */
.card-container .space-y-4 > div {
    /* Ensure proper spacing and containment */
    width: 100%;
    max-width: 100%;
    /* Provide adequate minimum width for form sections */
    min-width: 280px;
    overflow: visible;
}

/* Ensure card containers provide adequate space */
.card-container {
    /* Set minimum width to ensure proper layout */
    min-width: 320px;
}

/* Fix conversion icon layouts specifically */
.card-container .flex.items-center.gap-3 .conversion-icon {
    /* Ensure conversion icon doesn't take up too much space */
    flex-shrink: 0;
    width: 2.25rem;
    height: 2.25rem;
    min-width: 2.25rem;
    /* Ensure proper spacing */
    margin: 0 0.5rem;
}

/* Fix conversion icon positioning */
.conversion-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Ensure icon doesn't interfere with layout */
    z-index: 2;
    position: relative;
}

/* Ensure form labels don't cause overflow */
.form-label {
    display: block;
    width: 100%;
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Enhanced grid layouts within cards for proper 2-column arrangement */
.card-container .grid.grid-cols-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    width: 100%;
    /* Ensure grid has adequate minimum width */
    min-width: 280px;
    max-width: 100%;
}

.card-container .grid.grid-cols-2 > div {
    /* Ensure grid items have adequate minimum width */
    min-width: 120px;
    max-width: 100%;
    overflow: visible;
}

/* Enhanced responsive grid for small screens */
.card-container .grid.grid-cols-1.sm\:grid-cols-2 {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    width: 100%;
    min-width: 280px;
    max-width: 100%;
}

@media (min-width: 640px) {
    .card-container .grid.grid-cols-1.sm\:grid-cols-2 {
        grid-template-columns: 1fr 1fr;
    }
}

/* Fix 3-column grids to use 2-column layout for better spacing */
.card-container .grid.grid-cols-1.sm\:grid-cols-2.md\:grid-cols-3 {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    width: 100%;
    min-width: 280px;
    max-width: 100%;
}

@media (min-width: 640px) {
    .card-container .grid.grid-cols-1.sm\:grid-cols-2.md\:grid-cols-3 {
        grid-template-columns: 1fr 1fr;
    }
}

/* Override 3-column layout to use 2-column for better input field sizing */
@media (min-width: 768px) {
    .card-container .grid.grid-cols-1.sm\:grid-cols-2.md\:grid-cols-3 {
        grid-template-columns: 1fr 1fr;
    }
}

/* Ensure input groups maintain proper layout */
.card-container .flex.items-center {
    align-items: center;
    width: 100%;
    max-width: 100%;
    /* Provide adequate minimum width for flex containers */
    min-width: 280px;
    /* Allow wrapping on very small screens */
    flex-wrap: wrap;
    gap: 0.75rem;
}

@media (min-width: 640px) {
    .card-container .flex.items-center {
        flex-wrap: nowrap;
    }
}

/* Fix absolute positioned elements within cards */
.card-container .absolute {
    /* Ensure absolute elements don't interfere with layout */
    pointer-events: auto;
}

.card-container input[type="number"] {
    /* Ensure number inputs have adequate minimum width */
    min-width: 120px;
    width: 100%;
    max-width: 100%;
}

/* Specific fixes for select dropdowns */
.card-container select.form-select {
    min-width: 140px;
    width: 100%;
    max-width: 100%;
}

/* Handle column span classes in 2-column layout */
.card-container .grid .md\:col-span-1 {
    grid-column: span 1;
}

.card-container .grid .col-span-full {
    grid-column: 1 / -1;
}

/* Ensure proper spacing for grid items */
.card-container .grid > div {
    min-width: 120px;
    max-width: 100%;
}

/* Fix specific layout issues for production parameters cards */
.card-container .mb-4.grid {
    margin-bottom: 1rem;
    gap: 1rem;
}

/* Ensure proper layout for alignment switches and other full-width elements */
.card-container .col-span-full {
    grid-column: 1 / -1;
    width: 100%;
    min-width: 280px;
}

/* Modern Tabs Styles */
button[role="tab"] {
    position: relative;
    outline: none;
    white-space: nowrap;
    letter-spacing: 0.025em;
}

/* Premium Pills Tab Enhancement */
button[role="tab"][aria-selected="true"] {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
    letter-spacing: 0.025em;
}

/* Add sophisticated glow effect for active pills tabs */
.pills button[role="tab"][aria-selected="true"]::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, var(--primary), var(--secondary), var(--primary));
    border-radius: inherit;
    z-index: -1;
    opacity: 0.4;
    filter: blur(6px);
    transition: all 0.4s ease;
    animation: pulse-glow 3s ease-in-out infinite;
}

/* Add shimmer effect for active tabs */
.pills button[role="tab"][aria-selected="true"]::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    border-radius: inherit;
    z-index: 1;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% { opacity: 0.4; filter: blur(6px); }
    50% { opacity: 0.6; filter: blur(8px); }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Premium container background effect */
.pills::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05), rgba(255,255,255,0.1));
    border-radius: inherit;
    z-index: -1;
}

/* Enhanced icon styling for tabs */
button[role="tab"] svg {
    transition: all 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

button[role="tab"]:hover svg {
    transform: scale(1.15) rotate(5deg);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
}

button[role="tab"][aria-selected="true"] svg {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 6px rgba(99, 102, 241, 0.3));
    color: var(--primary);
}

/* Premium tab text styling */
button[role="tab"] span {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

button[role="tab"][aria-selected="true"] span {
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Custom scale utilities for tabs */
.hover\:scale-102:hover {
    transform: scale(1.02);
}

.scale-105 {
    transform: scale(1.05);
}

button[role="tab"]:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

button[role="tab"]::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -1;
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.2s ease;
}

button[role="tab"]:hover::before {
    opacity: 0.05;
    background-color: var(--primary);
}

button[role="tab"][aria-selected="true"]::before {
    opacity: 0.1;
    background-color: var(--primary);
}

/* Pill tabs specific styles */
button[role="tab"].rounded-full {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button[role="tab"].rounded-full[aria-selected="true"] {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.card-glow { box-shadow: 0 0 0 2px rgba(246, 156, 30, 0.2), 0 0 10px rgba(124, 58, 237, 0.3); transition: box-shadow 0.5s ease-out; }
@keyframes pulse-glow {
    0% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
    50% { box-shadow: 0 0 0 2px rgba(241,173,79,0.3), 0 0 15px rgba(124,58,237,0.4); }
    100% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
}
.pulse-glow { animation: pulse-glow 1.6s ease-in-out infinite; border-radius: 0.5rem; }
.fade-out-glow { animation: fade-out-pulse 1.5s forwards ease-out; border-radius: 0.5rem; }
@keyframes fade-out-pulse {
    0% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
    100% { box-shadow: 0 0 0 0 rgba(246,156,30,0), 0 0 0 rgba(124,58,237,0); }
}

body.dark-mode .grain-aligned { color: var(--success); }
body.dark-mode .grain-misaligned { color: var(--danger); }
body.dark-mode .text-red-500, body.dark-mode .text-red-600 { color: #f87171; }
body.dark-mode .text-danger { color: #f87171; }
body.dark-mode .text-success { color: #34d399; }
body.dark-mode .text-info { color: #60a5fa; }
body.dark-mode .text-warning { color: #fbbf24; }

/* Consolidated and Fixed Alignment Switch */
.alignment-switch {
    width: 60px; height: 32px; border-radius: 16px;
    background: linear-gradient(135deg, #10b981, #34d399);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
    position: relative;
    cursor: pointer;
    transition: var(--transition-premium);
}
.alignment-switch-toggle {
    position: absolute;
    top: 3px; left: 3px; width: 26px; height: 26px; border-radius: 50%;
    background: #ffffff; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); z-index: 2;
}
.alignment-switch.misaligned {
    background: linear-gradient(135deg, #ef4444, #f87171);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
}
.alignment-switch.misaligned .alignment-switch-toggle { transform: translateX(28px); }
.alignment-switch:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4), inset 0 2px 3px rgba(255, 255, 255, 0.3);
}
.alignment-switch.misaligned:hover {
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4), inset 0 2px 3px rgba(255, 255, 255, 0.3);
}
/* Fixed alignment mode text display logic */
.alignment-mode-text {
    font-size: 0.875rem;
    transition: all 0.3s ease;
    margin-left: 0.5rem;
    font-weight: 600;
}
.alignment-mode-text.aligned-text {
    color: #10b981;
}
.alignment-mode-text.misaligned-text {
    color: #ef4444;
}
body.dark-mode .alignment-mode-text.aligned-text { color: #34d399; }
body.dark-mode .alignment-mode-text.misaligned-text { color: #f87171; }

/* Consolidated and Fixed Double-Lip Switch */
.double-lip-switch {
    width: 60px; height: 32px; border-radius: 16px;
    background: linear-gradient(135deg, #10b981, #34d399);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
    position: relative;
    cursor: pointer;
    transition: var(--transition-premium);
}
.double-lip-switch-toggle {
    position: absolute;
    top: 3px; left: 3px; width: 26px; height: 26px; border-radius: 50%;
    background: #ffffff; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); z-index: 2;
}
.double-lip-switch.active {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
}
.double-lip-switch.active .double-lip-switch-toggle { transform: translateX(28px); }
.double-lip-switch:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4), inset 0 2px 3px rgba(255, 255, 255, 0.3);
}
.double-lip-switch.active:hover {
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4), inset 0 2px 3px rgba(255, 255, 255, 0.3);
}
/* Fixed double-lip text display */
.double-lip-text {
    font-size: 0.875rem;
    transition: all 0.3s ease;
    margin-left: 0.25rem;
    min-width: 1.5rem;
    display: inline-block;
    text-align: left;
    font-weight: 600;
}
.double-lip-switch ~ .double-lip-text:before {
    content: "x1";
    font-weight: 600;
    color: #10b981;
}
.double-lip-switch.active ~ .double-lip-text:before {
    content: "x2";
    font-weight: 600;
    color: #ef4444;
}
body.dark-mode .double-lip-switch ~ .double-lip-text:before { color: #34d399; }
body.dark-mode .double-lip-switch.active ~ .double-lip-text:before { color: #f87171; }

.loading-overlay {
    position: fixed; top: 0; left: 0; right: 0; bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex; align-items: center; justify-content: center; z-index: 2000;
    opacity: 0; visibility: hidden; transition: opacity 0.2s ease, visibility 0.2s ease;
}
body.dark-mode .loading-overlay { background-color: rgba(17, 24, 39, 0.7); }
.loading-overlay.visible { opacity: 1; visibility: visible; }
.loading-spinner {
    border: 5px solid var(--neutral-300); border-top: 5px solid var(--primary);
    border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite;
}
body.dark-mode .loading-spinner { border-color: var(--neutral-600); border-top-color: var(--primary-light); }
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

/* --- Tab Styles --- */
.tabs { display: flex; margin-bottom: 1.5rem; border-bottom: 2px solid var(--neutral-200); }
body.dark-mode .tabs { border-bottom-color: var(--neutral-700); }
.tab-button {
    padding: 0.75rem 1.5rem; /* Increased padding */
    cursor: pointer;
    border: none;
    background-color: transparent;
    font-size: 1rem; /* Slightly larger font */
    font-weight: 500;
    color: var(--neutral-500);
    margin-bottom: -2px; /* To align with the border-bottom of .tabs */
    border-bottom: 2px solid transparent;
    transition: color 0.2s ease, border-color 0.2s ease;
    outline: none;
    min-width: 100px; /* Add minimum width to stabilize tabs */
    text-align: center; /* Center text within the button */
}
.tab-button:hover { color: var(--primary); }
.tab-button.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
    font-weight: 600;
}
body.dark-mode .tab-button { color: var(--neutral-400); }
body.dark-mode .tab-button:hover { color: var(--primary-light); }
body.dark-mode .tab-button.active { color: var(--primary-light); border-bottom-color: var(--primary-light); }
.tab-content { display: none; }
.tab-content.active { display: block; animation: fadeIn 0.3s ease-out; }
@keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }

/* --- Selected Components Summary Panel Styles (Header Version) --- */
#summary-panel-container {
    position: relative; /* Changed from fixed */
    /* Removed bottom/right positioning */
    z-index: 1050;
    display: flex; /* Keep flex for alignment if needed */
    align-items: center; /* Align button vertically */
}
#summary-toggle-button {
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 36px; /* Match toggle height */
    height: 36px; /* Match toggle height */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15); /* Adjusted shadow */
    cursor: pointer;
    transition: var(--transition-transform), var(--transition-theme);
    border: none; /* Remove default border */
}
#summary-toggle-button:hover { transform: scale(1.1); background-color: var(--primary-dark); }
#summary-toggle-button svg { width: 18px; height: 18px; } /* Adjusted icon size */
#summary-panel {
    background-color: white;
    color: var(--neutral-800);
    border-radius: 0.5rem;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    padding: 1.5rem;
    position: fixed; /* Changed from absolute */
    top: 70px; /* To clear sticky header, adjust if header height changes */
    right: 0; /* Stick to the right edge of the viewport */
    /* Removed left, margin-left, margin-right */
    width: 320px; /* Reduced width for more compact display */
    max-height: 0; /* Initial state for animation */
    opacity: 0;
    overflow: hidden; /* Keep hidden initially for animation */
    visibility: hidden;
    transform: translateX(calc(100% + 1rem)) scale(0.95); /* Start off-screen to the right */
    transform-origin: top left; /* Animate from its top-left corner */
    transition: max-height var(--transition-duration) ease-out,
                opacity var(--transition-duration) ease-out,
                visibility 0s linear var(--transition-duration), /* Delay visibility until opacity transition fully ends */
                transform var(--transition-duration) ease-out,
                background-color var(--transition-duration) var(--transition-timing),
                color var(--transition-duration) var(--transition-timing),
                box-shadow var(--transition-duration) var(--transition-timing);
    z-index: 1040; /* Ensure it's above header, below modals */
    /* Performance optimizations to prevent flickering during scroll */
    will-change: transform, opacity, max-height;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    /* Prevent paint flashing during scroll */
    contain: paint;
    /* Force GPU acceleration */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
#summary-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    /* Prevent layout shifts and improve rendering */
    contain: layout;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}
.summary-static-title {
    /* Only prevent unwanted animations while keeping language/theme transitions */
    /* Improve rendering with hardware acceleration */
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    /* Prevent layout shifts during scroll */
    contain: content;
    /* Ensure the element has a stable position */
    position: relative;
    z-index: 1;
}
#summary-close-button {
    background: none;
    border: none;
    color: var(--neutral-500);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-theme);
}
#summary-close-button:hover {
    color: var(--neutral-700);
    background-color: var(--neutral-200);
}
body.dark-mode #summary-close-button {
    color: var(--neutral-400);
}
body.dark-mode #summary-close-button:hover {
    color: var(--neutral-200);
    background-color: var(--neutral-700);
}
#summary-panel.visible {
    max-height: calc(100vh - 70px - 1rem); /* Max height considering header and some bottom margin */
    opacity: 1;
    visibility: visible;
    transform: translateX(0) scale(1); /* Slide into view */
    transition: max-height var(--transition-duration) ease-out,
                opacity var(--transition-duration) ease-out,
                visibility 0s linear 0s, /* Make visible immediately when starting to show */
                transform var(--transition-duration) ease-out,
                background-color var(--transition-duration) var(--transition-timing),
                color var(--transition-duration) var(--transition-timing),
                box-shadow var(--transition-duration) var(--transition-timing);
    overflow-y: auto; /* Add scroll if content overflows */
    /* Additional performance optimizations for visible state */
    contain: content; /* Improve rendering performance by isolating content */
}
body.dark-mode #summary-toggle-button {
    background-color: var(--primary-light);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
}
body.dark-mode #summary-toggle-button:hover { background-color: var(--primary); }
body.dark-mode #summary-panel {
    background-color: var(--neutral-800);
    color: var(--neutral-100);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}
.summary-item {
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px dashed var(--neutral-200);
    transition: border-color var(--transition-duration) var(--transition-timing);
    /* Performance optimizations */
    contain: content;
    will-change: transform;
    transform: translateZ(0);
}
body.dark-mode .summary-item { border-bottom-color: var(--neutral-700); }

/* Premium Enhancement Styles */
.premium-section {
    position: relative;
    overflow: hidden;
}

.premium-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Premium Header Styles */
.premium-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 80px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-header::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
}

/* Premium Tab Container */
.premium-tab-container {
    position: relative;
    margin-left: -0.5rem;
}

/* Enhanced Glass Tabs */
.enhanced-glass-tabs {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-glass-tabs::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.3) 0%, 
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: inherit;
    z-index: -1;
}

.enhanced-glass-tabs::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.2) 0%,
        transparent 70%
    );
    opacity: 0.5;
    pointer-events: none;
    z-index: -1;
    transform: translateZ(0);
    animation: shine 8s infinite linear;
}

@keyframes shine {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.premium-tab-glass::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.3) 100%);
    border-radius: inherit;
    pointer-events: none;
}

.premium-active-tab {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-active-tab::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(99, 102, 241, 0.15) 0%,
        rgba(139, 92, 246, 0.1) 100%);
    border-radius: inherit;
    pointer-events: none;
    z-index: -1;
}

.premium-active-tab::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to right,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    100% { transform: translateX(100%); }
}

.premium-inactive-tab {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-inactive-tab:hover::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(99, 102, 241, 0.08) 0%,
        rgba(139, 92, 246, 0.05) 100%);
    border-radius: inherit;
    pointer-events: none;
    z-index: -1;
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

/* Premium Control Group Styles */
.premium-control-group {
    position: relative;
    display: flex;
    align-items: center;
}

/* Premium Language Switch */
.premium-lang-switch {
    position: relative;
    width: 100px;
    height: 44px;
    border-radius: 22px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 6px;
}

.premium-switch-background {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, #ef4444 0%, #f87171 50%, #fca5a5 100%);
    border-radius: inherit;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 8px 25px rgba(239, 68, 68, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        inset 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.premium-lang-switch.active .premium-switch-background {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 50%, #8b5cf6 100%);
    box-shadow:
        0 8px 25px rgba(79, 70, 229, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        inset 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.premium-lang-switch:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(239, 68, 68, 0.4);
}

.premium-lang-switch.active:hover {
    box-shadow: 0 12px 30px rgba(79, 70, 229, 0.4);
}

.premium-switch-label {
    position: relative;
    z-index: 2;
    font-size: 0.8rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.9);
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.premium-switch-toggle {
    position: absolute;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 50%;
    top: 6px;
    left: 6px;
    z-index: 3;
    transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.15),
        inset 0 1px 2px rgba(255, 255, 255, 0.8);
}

.premium-lang-switch.active .premium-switch-toggle {
    transform: translateX(56px);
}

/* Premium Theme Switch */
.premium-theme-switch {
    position: relative;
    width: 100px;
    height: 44px;
    border-radius: 22px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 6px;
}

.premium-theme-switch .premium-switch-background {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #fcd34d 100%);
    box-shadow:
        0 8px 25px rgba(245, 158, 11, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        inset 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.premium-theme-switch.dark .premium-switch-background {
    background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
    box-shadow:
        0 8px 25px rgba(31, 41, 55, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.1),
        inset 0 -2px 4px rgba(0, 0, 0, 0.2);
}

.premium-theme-switch:hover {
    transform: translateY(-2px);
}

.premium-theme-icon {
    position: relative;
    z-index: 2;
    width: 20px;
    height: 20px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.premium-theme-switch.dark .premium-switch-toggle {
    transform: translateX(56px);
}

/* Premium Summary Button */
.premium-summary-button {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 16px;
    cursor: pointer;
    border: none;
    outline: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.premium-button-background {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
    border-radius: inherit;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 8px 25px rgba(99, 102, 241, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        inset 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.premium-summary-button:hover .premium-button-background {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #9333ea 100%);
    box-shadow:
        0 12px 30px rgba(99, 102, 241, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        inset 0 -2px 4px rgba(0, 0, 0, 0.15);
}

.premium-summary-button:hover {
    transform: translateY(-2px) scale(1.05);
}

.premium-summary-button:active {
    transform: translateY(0) scale(0.98);
}

.premium-button-content {
    position: relative;
    z-index: 2;
    color: white;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.premium-summary-button:hover .premium-button-content {
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .premium-header {
        height: 70px;
        padding: 0 1rem;
    }

    .premium-lang-switch,
    .premium-theme-switch {
        width: 80px;
        height: 36px;
    }

    .premium-switch-toggle {
        width: 26px;
        height: 26px;
        top: 5px;
        left: 5px;
    }

    .premium-lang-switch.active .premium-switch-toggle,
    .premium-theme-switch.dark .premium-switch-toggle {
        transform: translateX(44px);
    }

    .premium-summary-button {
        width: 40px;
        height: 40px;
        border-radius: 12px;
    }

    .premium-control-group {
        margin: 0 0.25rem;
    }
}

/* Enhanced Table Styles */
.table-container {
    background: var(--card-gradient-light);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-md);
}

body.dark-mode .table-container {
    background: var(--card-gradient-dark);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Premium Animation Classes */
.animate-slideUp {
    animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Enhanced Focus States */
.form-input:focus-within,
.form-select:focus-within {
    transform: translateY(-1px);
}

/* Premium Hover Effects */
.hover-lift {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-2px);
}

/* Gradient Text Effect */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Premium Border Gradient */
.border-gradient {
    position: relative;
    background: var(--card-gradient-light);
    border-radius: 1rem;
}

.border-gradient::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: var(--primary-gradient);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
}

body.dark-mode .border-gradient {
    background: var(--card-gradient-dark);
}
.summary-item:last-child { margin-bottom: 0; padding-bottom: 0; border-bottom: none; }
.summary-item-label { font-size: 0.8rem; color: var(--neutral-500); transition: var(--transition-theme); }
body.dark-mode .summary-item-label { color: var(--neutral-400); }
.summary-item-value { font-weight: 500; word-break: break-all; transition: var(--transition-theme); }
.summary-total {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--neutral-300);
    transition: border-color var(--transition-duration) var(--transition-timing);
    /* Performance optimizations */
    contain: content;
    will-change: transform;
    transform: translateZ(0);
}
body.dark-mode .summary-total { border-top-color: var(--neutral-600); }
.summary-total-label { font-weight: 600; font-size: 1rem; transition: var(--transition-theme); }
.summary-total-value { font-weight: 700; font-size: 1.125rem; color: var(--primary); transition: var(--transition-theme); }
body.dark-mode .summary-total-value { color: var(--primary-light); }

/* "Select this Option" button specific style */
.btn-select-option { /* Use btn-success styling as base */
    background-color: var(--success); color: white; border: 1px solid #059669;
}
.btn-select-option:hover { background-color: #059669; }
.btn-select-option.selected {
    background-color: var(--accent); color: white; border-color: #d97706; /* Amber-600 */
}
.btn-select-option.selected:hover { background-color: #d97706; }

/* Accent button style for selected options */
.btn-accent {
    background-color: var(--accent); color: white; border: 1px solid #d97706; /* Amber-600 */
}
.btn-accent:hover { background-color: #d97706; }

/* Results Section Styling */
.results-section {
    margin: 2rem 0;
    overflow: hidden;
    border-radius: 1rem;
    background: var(--card-gradient-light);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--glass-border);
    transition: var(--transition-premium);
}

body.dark-mode .results-section {
    background: var(--card-gradient-dark);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--glass-border);
}

body.dark-mode .results-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.results-grid-container {
    padding: 1.5rem;
}

.results-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    width: 100%;
}

@media (min-width: 768px) {
    .results-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Result Card Premium Styling */
.result-card {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--card-gradient-light);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.result-card.best-option {
    position: relative;
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.2);
}

.result-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.grain-aligned {
    color: var(--success);
}

.grain-misaligned {
    color: var(--warning);
}

/* Loading State */
.results-section-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background: var(--card-gradient-light);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    margin: 2rem 0;
}

body.dark-mode .results-section-loading {
    background: var(--card-gradient-dark);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(99, 102, 241, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .results-grid {
        grid-template-columns: 1fr;
    }

    .results-header {
        padding: 1rem 1.5rem;
    }

    .results-grid-container {
        padding: 1rem;
    }
}
}
