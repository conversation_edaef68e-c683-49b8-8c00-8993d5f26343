'use client';

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useSummary } from '@/contexts/SummaryContext';
import Tabs from '@/components/ui/Tabs';

type ComponentType = 'innerText' | 'cover' | 'endpapers';

interface HeaderProps {
  activeTab: ComponentType;
  onTabChange: (tab: ComponentType) => void;
}

export default function Header({ activeTab, onTabChange }: HeaderProps) {
  const { theme, toggleTheme } = useTheme();
  const { language, toggleLanguage } = useLanguage();
  const { togglePanel } = useSummary();

  const tabs = [
    {
      id: 'innerText',
      label: 'Inner Text',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      id: 'cover',
      label: 'Cover',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76" />
        </svg>
      )
    },
    {
      id: 'endpapers',
      label: 'Endpapers',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
        </svg>
      )
    }
  ];

  const handleTabChange = (tabId: string) => {
    onTabChange(tabId as ComponentType);
  };

  return (
    <header className='fixed top-0 left-0 right-0 z-[1000] w-full premium-header'>
      {/* Enhanced Premium Glass Morphism Background */}
      <div className='absolute inset-0 bg-gradient-to-r from-white/90 via-white/85 to-white/90 dark:from-neutral-900/90 dark:via-neutral-900/85 dark:to-neutral-900/90 backdrop-blur-xl border-b border-white/40 dark:border-neutral-700/40 shadow-lg'></div>

      {/* Enhanced gradient overlay with more vibrant colors */}
      <div className='absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 pointer-events-none'></div>

      {/* Subtle light reflection effect */}
      <div className='absolute inset-0 overflow-hidden pointer-events-none'>
        <div className='absolute -inset-[200%] bg-gradient-to-br from-white/20 via-transparent to-transparent rotate-45 transform translate-x-full animate-slow-pulse opacity-50'></div>
      </div>

      {/* Main header content with improved spacing */}
      <div className='relative mx-auto flex max-w-7xl items-center justify-between px-6 lg:px-8 py-5'>
        {/* Left section - Enhanced Tab Navigation */}
        <div className='flex items-center flex-1'>
          <div className='premium-tab-container'>
            <Tabs
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={handleTabChange}
              variant="clean"
              size="lg"
              className="premium-tabs enhanced-glass-tabs"
            />
          </div>
        </div>

        {/* Right section - Controls */}
        <div className='flex items-center space-x-4'>
          {/* Language Switcher */}
          <div className='premium-control-group'>
            <div
              onClick={toggleLanguage}
              className={`premium-lang-switch lang-switch ${language === 'zh' ? 'active' : ''}`}
              title='Switch Language'
            >
              <div className='premium-switch-background'></div>
              <span className='premium-switch-label lang-switch-label en'>EN</span>
              <div className='premium-switch-toggle lang-switch-toggle'></div>
              <span className='premium-switch-label lang-switch-label zh'>中</span>
            </div>
          </div>

          {/* Theme Switcher */}
          <div className='premium-control-group'>
            <div
              onClick={toggleTheme}
              className={`premium-theme-switch theme-switch ${theme === 'dark' ? 'dark' : ''}`}
              title='Switch Theme'
            >
              <div className='premium-switch-background'></div>

              {/* Sun Icon */}
              <svg className="premium-theme-icon theme-switch-icon sun" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
              </svg>

              <div className='premium-switch-toggle theme-switch-toggle'></div>

              {/* Moon Icon */}
              <svg className="premium-theme-icon theme-switch-icon moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path fillRule="evenodd" d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" clipRule="evenodd" />
              </svg>
            </div>
          </div>

          {/* Summary Panel Toggle */}
          <div className='premium-control-group'>
            <button
              onClick={togglePanel}
              className='premium-summary-button'
              title='View Selected Components Summary'
            >
              <div className='premium-button-background'></div>
              <div className='premium-button-content'>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
